/**
 * MenuTopNavigation Styles - Sistema de Menú Top con 3 Modos
 * 
 * Concilia el diseño legacy refinado con la nueva arquitectura:
 * - TopMenu vertical con iconos grandes y labels pequeños
 * - MenuOfSections con acordeones y navegación jerárquica  
 * - Sistema de branding dinámico con colores personalizables
 * - Layout de 2 columnas: menú top vertical (64px) + secciones expandibles
 * 
 * Migrado desde:
 * - src/proyector/components/common/MenuTop.css
 * - src/modules/panel/MenuMainContent.js (estilos inline)
 * - src/modules/panel/MenuOfSections.js (estilos inline)
 */

/* ===== CONTENEDOR PRINCIPAL ===== */
.menu-top {
  position: relative;
  z-index: 1000;
  background: var(--ion-background-color);
  border-bottom: 1px solid var(--ion-border-color);
  transition: all 0.3s ease;
}

.menu-top.collapsed {
  height: auto;
}

.menu-top.expanded {
  height: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== HEADER DEL MENU TOP ===== */
.menu-top-header {
  position: relative;
  z-index: 1001;
}

.menu-top-segment {
  --background: transparent;
  --background-checked: var(--ion-color-primary-tint);
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary-contrast);
  --border-radius: 8px;
  --border-width: 1px;
  --border-color: var(--ion-color-light-shade);
  margin: 8px 16px;
}

.menu-top-segment ion-segment-button {
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  min-height: 40px;
  transition: all 0.2s ease;
}

.menu-top-segment ion-segment-button:hover {
  --background: var(--ion-color-light-tint);
}

.menu-top-segment ion-segment-button ion-icon {
  margin-right: 6px;
  font-size: 18px;
}

.menu-top-segment ion-segment-button ion-label {
  font-size: 14px;
  font-weight: 500;
}

/* ===== CONTENIDO EXPANDIBLE ===== */
.menu-top-content {
  background: var(--ion-color-light);
  border-top: 1px solid var(--ion-border-color);
  min-height: 400px;
  max-height: 60vh;
}

/* ===== SIDE MENU LEGACY STYLES ===== */
.side-menu {
  --background: var(--ion-color-light);
}

/* ===== TOP MENU VERTICAL (MIGRADO DE LEGACY) ===== */
.menu-top-content .flex.flex-row > .simplebar-wrapper:first-child {
  background: var(--ion-color-light-shade);
  border-right: 1px solid var(--ion-border-color);
}

/* Ripple effect para items del top menu */
.ripple-parent {
  position: relative;
  overflow: hidden;
}

.ripple-parent ion-ripple-effect {
  color: var(--ion-color-primary);
}

/* Clases de branding legacy */
.bg-brand-primary-tint {
  background-color: var(--ion-color-primary-tint);
}

.bg-brand-medium-tint {
  background-color: var(--ion-color-medium-tint);
}

.text-brand-medium-contrast {
  color: var(--ion-color-medium-contrast);
}

.text-brand-light {
  color: var(--ion-color-light);
}

.text-brand-dark {
  color: var(--ion-color-dark);
}

.font-brand-main {
  font-family: var(--ion-font-family);
}

/* ===== ACORDEONES (MIGRADO DE MENUOFSECTIONS) ===== */
.menu-top-content ion-accordion-group {
  background: transparent;
}

.menu-top-content ion-accordion {
  background: var(--ion-background-color);
  margin-bottom: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.menu-top-content ion-accordion ion-item[slot="header"] {
  --background: var(--ion-color-light);
  --color: var(--ion-color-dark);
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 48px;
  font-weight: 500;
}

.menu-top-content ion-accordion ion-item[slot="header"]:hover {
  --background: var(--ion-color-light-tint);
}

.menu-top-content ion-accordion[slot="content"] {
  --background: var(--ion-background-color);
}

.menu-top-content ion-accordion ion-item {
  --padding-start: 32px;
  --padding-end: 16px;
  --min-height: 40px;
  --background: transparent;
  --color: var(--ion-color-medium-shade);
  transition: all 0.2s ease;
}

.menu-top-content ion-accordion ion-item:hover {
  --background: var(--ion-color-light-tint);
  --color: var(--ion-color-dark);
}

.menu-top-content ion-accordion ion-item.selected {
  --background: var(--ion-color-primary-tint);
  --color: var(--ion-color-primary-contrast);
  font-weight: 500;
}

.menu-top-content ion-accordion ion-item ion-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* ===== MENU TOGGLE ITEMS ===== */
.menu-top-content ion-menu-toggle ion-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-top-content ion-menu-toggle ion-item:hover {
  --background: var(--ion-color-light-tint);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .menu-top-content {
    max-height: 50vh;
  }
  
  .menu-top-content .flex.flex-row {
    flex-direction: column;
  }
  
  .menu-top-content .flex.flex-row > .simplebar-wrapper:first-child {
    min-width: 100%;
    max-width: 100%;
    width: 100%;
    max-height: 120px;
    border-right: none;
    border-bottom: 1px solid var(--ion-border-color);
  }
  
  .menu-top-content .flex.flex-row > .simplebar-wrapper:first-child .flex.flex-col {
    flex-direction: row;
    justify-content: space-around;
    padding: 8px 0;
  }
  
  .menu-top-segment ion-segment-button ion-label {
    display: none;
  }
  
  .menu-top-segment ion-segment-button ion-icon {
    margin-right: 0;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .menu-top-content {
    max-height: 40vh;
  }
  
  .menu-top-segment {
    margin: 4px 8px;
  }
  
  .menu-top-segment ion-segment-button {
    --padding-start: 8px;
    --padding-end: 8px;
    --padding-top: 6px;
    --padding-bottom: 6px;
    min-height: 36px;
  }
}

/* ===== ANIMACIONES ===== */
.menu-top-content {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-top-segment ion-segment-button {
  transition: all 0.2s ease;
}

.menu-top-segment ion-segment-button:active {
  transform: scale(0.98);
}

/* ===== SCROLLBAR PERSONALIZADO (SIMPLEBAR) ===== */
.menu-top-content .simplebar-scrollbar::before {
  background-color: var(--ion-color-medium-tint);
  border-radius: 4px;
}

.menu-top-content .simplebar-track.simplebar-vertical {
  width: 6px;
}

.menu-top-content .simplebar-track.simplebar-horizontal {
  height: 6px;
}

/* ===== ESTADOS DE HOVER Y FOCUS ===== */
.menu-top ion-chip {
  transition: all 0.2s ease;
}

.menu-top ion-chip:hover {
  transform: scale(1.02);
}

.menu-top ion-button {
  transition: all 0.2s ease;
}

.menu-top ion-button:hover {
  --background: var(--ion-color-light-tint);
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .menu-top {
    --ion-background-color: var(--ion-color-dark);
    --ion-border-color: var(--ion-color-dark-shade);
  }
  
  .menu-top-content {
    --ion-color-light: var(--ion-color-dark-tint);
    --ion-color-light-shade: var(--ion-color-dark-shade);
    --ion-color-light-tint: var(--ion-color-dark);
  }
  
  .bg-brand-primary-tint {
    background-color: var(--ion-color-primary-shade);
  }
  
  .bg-brand-medium-tint {
    background-color: var(--ion-color-medium-shade);
  }
  
  .text-brand-medium-contrast {
    color: var(--ion-color-light);
  }
}
