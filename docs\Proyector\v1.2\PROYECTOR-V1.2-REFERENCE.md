# Proyector v1.2 - Referencia General del Sistema

**ID**: proyector-v1.2-reference
**Versión**: 1.2
**Fecha**: Enero 2025
**Estado**: 📚 **REFERENCIA COMPLETA** - Documento maestro para agentes AI sobre arquitectura Proyector v1.2

## 🎯 **RESUMEN EJECUTIVO**

El Proyector v1.2 es un **ecosistema completamente integrado** basado en la arquitectura **Sites-CodeBlocks** que permite crear, gestionar y evolucionar sitios web inteligentes de forma completamente dinámica. Todo el sistema se basa en el principio fundamental: **TODO ES SPECS ↔ CODEBLOCKS**.

## 🏗️ **ARQUITECTURA FUNDAMENTAL**

### **Principio Central: Sites-CodeBlocks-Specs**
```
PROYECTOR = SITES[configurables] + CODEBLOCKS[funcionalidad] + SPECS[metadatos]
```

- **Sites**: Espacios de trabajo independientes multi-tenant
- **CodeBlocks**: Funcionalidad ejecutable (static/dynamic)
- **Specs**: Metadatos que referencian CodeBlocks para 4 propósitos:
  1. 🎯 **Orquestación de Procesos** - Workflows, automatizaciones
  2. 🎨 **Renderizado de UI** - Componentes, layouts dinámicos
  3. 💾 **Almacenamiento de Datos** - Validaciones, transformaciones
  4. ⚙️ **Configuración Dinámica** - Settings, personalizaciones

### **Separación Backend/Frontend Estricta**
- **Backend**: `functions-js/` - NUNCA importa desde `public/` o `src/`
- **Frontend**: `src/` - Usa HyperContext para datos
- **Static Resources**: `public/staticModules/` y `public/staticCodeblocks/`
- **Intermediario**: HyperContext en Firestore como única fuente de verdad

## 🌐 **HYPERCONTEXT: NOMENCLATURA Y PATHS**

### **Diferencias Críticas: HyperContext vs Firebase**

#### **HyperContext Paths (Notación de Puntos)**
```javascript
// ✅ CORRECTO: HyperContext notation
"sites[siteId].entities[entityType][entityId]"
"users[userId].profile"
"sites[main].specs[specId]"
```

#### **Firebase Paths (Estructura Real)**
```javascript
// Estructura Firestore resultante:
// HyperContext_dev/sites/siteId/entities/entityType/entityId
// HyperContext_dev/users/userId/profile
// HyperContext_dev/sites/main/specs/specId
```

### **Prefijos Automáticos**
- **Producción**: `HyperContext`
- **Desarrollo/Híbrido**: `HyperContext_dev`

### **Tipos de Datos por Estructura**
- **📄 Documento Base**: < 100KB - Datos principales
- **📋 Documento Individual**: < 500KB - Extensión específica  
- **📚 Subcollection**: Ilimitado - Datos dinámicos
- **🔧 Spec**: Metadato que referencia CodeBlocks
- **🧩 CodeBlock**: Funcionalidad ejecutable

## 🏢 **ARQUITECTURA DE SITES**

### **Estructura Site-Specific (NO Global)**
```javascript
sites[siteId] = {
  // Configuración básica
  name: "Mi Empresa",
  slug: "mi-empresa", // URL: mi-empresa.proyector.app
  
  // Plan y límites
  plan: {
    id: "professional",
    limits: { storage: "10GB", aiTokens: 100000, users: 5 },
    usage: { storage: "2.5GB", aiTokens: 15000, users: 2 }
  },
  
  // Branding personalizable
  branding: {
    colors: { primary: "#1976d2", secondary: "#dc004e" },
    typography: { headings: "Roboto", body: "Open Sans" },
    assets: { logo: "sites/siteId/assets/logo.svg" }
  },
  
  // Módulos habilitados según plan
  modules: {
    pages: { enabled: true, config: { enableSEO: true } },
    contacts: { enabled: true, config: { enableCRM: true } }
  }
}

// Subcollections del site
sites[siteId].specs[]        // 📚 Metadatos con referencias CodeBlocks
sites[siteId].codeblocks[]   // 📚 Funcionalidad ejecutable
sites[siteId].entities[]     // 📚 Datos del site
sites[siteId].pages[]        // 📚 Contenido del site
```

### **Site 'main' = Global**
- El site `main` actúa como **global** porque es el host site
- Configuraciones del sistema se almacenan en `sites[main].*`
- **NO existe configuración global fuera de sites**

## 🧩 **SISTEMA CODEBLOCKS**

### **Tipos de CodeBlocks**

#### **Static CodeBlocks** (`public/staticCodeblocks/`)
- **Propósito**: Funcionalidades core del sistema
- **Gestión**: Mantenidos por equipo Proyector
- **Ejemplos**: `cart-widget`, `analytics-dashboard`, `entity-form-block`
- **Estructura**:
  ```
  public/staticCodeblocks/[blockId]/
  ├── codeblock.json    # Configuración principal
  ├── component.jsx     # Componente React
  ├── server.js         # Lógica backend (opcional)
  ├── styles.css        # Estilos (opcional)
  └── template.json     # Template JSON (opcional)
  ```

#### **Dynamic CodeBlocks** (HyperContext)
- **Propósito**: Funcionalidades custom por usuario
- **Gestión**: Creados via BotAgents/chat
- **Storage**: `sites[siteId].dynamicCodeblocks[]`
- **Marketplace**: Compartir entre usuarios

### **🔒 HERMETICIDAD CRÍTICA DE STATICCODEBLOCKS**

#### **Principio Fundamental: Hermeticidad Total**
Los staticCodeblocks deben ser **COMPLETAMENTE HERMÉTICOS**:
- ❌ **PROHIBIDO**: Importar desde `src/proyector/`
- ❌ **PROHIBIDO**: Importar desde `../../../`
- ❌ **PROHIBIDO**: Usar `useAuth`, `useStore`, `useSite` directamente
- ❌ **PROHIBIDO**: Acceder a UniversalRenderer desde src/
- ✅ **CORRECTO**: Solo recibir lo que necesitan via props

#### **Patrón de Inyección de Dependencias**
```javascript
// ❌ INCORRECTO: Violación de hermeticidad
import { useAuth } from '../../../src/proyector/store';
import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';

export const MessageTextBlockController = ({ message, isOwn }) => {
  const { user } = useAuth(); // ❌ VIOLACIÓN
  // ...
};

// ✅ CORRECTO: Hermeticidad total
export const MessageTextBlockController = ({
  message,
  isOwn,
  user,           // ✅ Inyectado via props
  onAction,       // ✅ Callbacks inyectados
  UniversalRenderer, // ✅ Renderer inyectado
  store           // ✅ Store inyectado si necesario
}) => {
  // ✅ Solo usa lo que recibe via props
  // ...
};
```

#### **Violaciones Identificadas y Corrección Requerida**
```javascript
// VIOLACIONES CRÍTICAS DETECTADAS:
// 1. public/staticCodeblocks/message-text-block/component.jsx
//    - import { useAuth } from '../../../src/proyector/store';
//    - const { user } = useAuth();

// 2. public/staticCodeblocks/message-pageblocks-block/component.jsx
//    - import { useAuth } from '../../../src/proyector/store';
//    - import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';
//    - const { user } = useAuth();

// 3. public/staticCodeblocks/chat-track-layout/component.jsx
//    - import { useAuth, useSite } from '../../../src/proyector/store';
//    - import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';
//    - const { user } = useAuth();
//    - const { current: currentSite } = useSite();
```

#### **Dependencias Permitidas en StaticCodeblocks**
```javascript
// ✅ PERMITIDO: Librerías externas declaradas en codeblock.json
import React, { useState, useEffect } from 'react';
import { IonCard, IonButton } from '@ionic/react';
import { motion } from 'framer-motion';
import { sendOutline } from 'ionicons/icons';

// ✅ PERMITIDO: Archivos locales del mismo codeblock
import './styles.css';

// ❌ PROHIBIDO: Cualquier import desde fuera del codeblock
import { useAuth } from '../../../src/proyector/store';
import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';
```

### **Static Modules** (`public/staticModules/`)
- **Propósito**: Configuraciones JSON + Specs
- **Ejemplos**: `contacts`, `sales`, `pages`, `analytics`
- **Estructura**:
  ```
  public/staticModules/[moduleId]/
  ├── module.json       # Configuración principal
  ├── entities.json     # Definiciones de entidades
  ├── pages.json        # Páginas del módulo
  ├── aiTools.json      # Herramientas AI (opcional)
  └── specs.json        # Especificaciones (opcional)
  ```

## 💬 **SISTEMA CHAT INTEGRAL**

### **Arquitectura Chat Basada en PageBlocks**

#### **Principio Fundamental: Todo es PageBlocks**
El sistema de chat del Proyector está completamente basado en la arquitectura PageBlocks:
- **Chat Track Layout**: PageBlock principal que orquesta conversaciones
- **Message Blocks**: Cada mensaje es un PageBlock individual
- **Tools Integration**: Herramientas AI integradas via PageBlocks
- **Real-time Sync**: onSnapshot de Firestore para actualizaciones

#### **Componentes Chat Implementados**
```javascript
// Chat Track Principal
public/staticCodeblocks/chat-track-layout/
├── codeblock.json    # Configuración chat track
├── component.jsx     # Layout principal conversación
└── styles.css        # Estilos chat

// Message Blocks
public/staticCodeblocks/message-text-block/
├── codeblock.json    # Configuración mensaje texto
├── component.jsx     # Componente mensaje texto
└── styles.css        # Estilos mensaje

public/staticCodeblocks/message-pageblocks-block/
├── codeblock.json    # Configuración mensaje pageblocks
├── component.jsx     # Renderizado recursivo pageblocks
└── styles.css        # Estilos pageblocks

// Chat System Completo
public/staticCodeblocks/chat-system/
├── codeblock.json    # Sistema chat completo
├── component.jsx     # Interface chat avanzada
└── styles.css        # Estilos sistema
```

#### **Flujo de Datos Chat**
```
1. Usuario escribe mensaje → Frontend
2. Mensaje enviado → Backend Cloud Function
3. Procesamiento AI → BotAgents/Tools
4. Respuesta generada → HyperContext Storage
5. onSnapshot trigger → Frontend actualización
6. UniversalRenderer → Renderiza PageBlocks
```

### **🚨 INCONSISTENCIAS ARQUITECTURALES DETECTADAS**

#### **Controllers Hardcodeados (ELIMINAR)**
```javascript
// ❌ INCONSISTENCIA: Controllers hardcodeados en src/
src/proyector/pageBlocks/controllers/lateralChatController.js  // ELIMINAR
src/components/chat/LateralChatInterface.jsx                   // MIGRAR
src/components/chat/ChatIntegration.jsx                        // MIGRAR

// ✅ SOLUCIÓN: Todo debe ser staticCodeblocks
public/staticCodeblocks/lateral-chat-interface/
public/staticCodeblocks/chat-integration/
public/staticCodeblocks/chat-tools-panel/
```

#### **Menu Top y Lateral (MIGRAR A STATICCODEBLOCKS)**
```javascript
// ❌ INCONSISTENCIA: Menus hardcodeados
src/proyector/components/MenuTop.jsx                          // MIGRAR
src/proyector/components/MenuLateral.jsx                     // MIGRAR

// ✅ SOLUCIÓN: Menus como staticCodeblocks
public/staticCodeblocks/menu-top-navigation/
├── codeblock.json    # Configuración menu top
├── component.jsx     # Componente menu top
└── styles.css        # Estilos menu

public/staticCodeblocks/menu-lateral-navigation/
├── codeblock.json    # Configuración menu lateral
├── component.jsx     # Componente menu lateral
└── styles.css        # Estilos menu
```

#### **BotAgents, BotFlows, BotConfigs (STATICCODEBLOCKS)**
```javascript
// ✅ ARQUITECTURA CORRECTA: Todo como staticCodeblocks
public/staticCodeblocks/bot-agents-panel/
├── codeblock.json    # Panel gestión agentes
├── component.jsx     # Interface agentes
└── styles.css        # Estilos panel

public/staticCodeblocks/bot-flows-editor/
├── codeblock.json    # Editor flujos bot
├── component.jsx     # Interface ReactFlow
└── styles.css        # Estilos editor

public/staticCodeblocks/bot-configs-manager/
├── codeblock.json    # Gestión configuraciones
├── component.jsx     # Interface configuración
└── styles.css        # Estilos manager
```

## 🎨 **PAGEBLOCKS UNIVERSAL ENGINE**

### **Arquitectura JSON-Driven**
- **Principio**: Eliminar componentes React hardcodeados
- **Separación**: Templates JSON + Controllers JavaScript
- **Renderizado**: UniversalRenderer convierte JSON → React

### **Componentes Clave**
```javascript
// UniversalRenderer.jsx - Motor principal
src/proyector/pageBlocks/renderers/UniversalRenderer.jsx

// ComponentFactory.js - Factory de componentes
src/proyector/pageBlocks/universal/ComponentFactory.js

// TemplateLibrary.js - Biblioteca templates
src/proyector/pageBlocks/universal/TemplateLibrary.js

// ControllerManager.js - Gestión controllers
src/proyector/pageBlocks/universal/ControllerManager.js
```

### **Resolución Jerárquica**
1. **Static**: Templates JSON estáticos
2. **Dynamic**: Módulos dinámicos desde HyperContext
3. **Site-specific**: Overrides específicos por site
4. **Fallback**: Automático a static si dynamic falla

## 🧠 **SISTEMA AI Y ZUSTAND**

### **Store Unificado** (`src/proyector/store/`)
- **Arquitectura**: Zustand con Firebase real-time sync
- **Slices principales**: `authSlice`, `sitesSlice`, `entitiesSlice`, `formsSlice`
- **Middleware**: `firebaseSyncMiddleware`, `persistenceMiddleware`

### **Chat Lateral** (`src/proyector/store/useChat.js`)
- **Propósito**: Interface principal del Proyector
- **Herramientas**: Dinámicas según contexto del site
- **AI Integration**: BotAgents con memoria agéntica

### **BotAgents** (`functions-js/proyector/ai/`)
- **Funcionalidad**: Generación de código dinámico
- **Flujos**: Graph-based con ReactFlow UI
- **Memoria**: Sistema de contexto y aprendizaje

## 🔐 **AUTENTICACIÓN Y PERMISOS**

### **Sistema de Usuarios**
```javascript
// Usuario global (único dato global)
users[userId] = {
  publicProfile: { name, avatar },
  privateData: { email, preferences },
  metadata: { createdAt, lastLogin }
}

// Perfil específico por site
sites[siteId].profiles[userId] = { role, permissions, siteSpecificData }

// Credenciales por site
sites[siteId].credentials[userId] = { permissions, accessLevel }
```

### **🚨 PROBLEMA CRÍTICO: CREDENCIALES DUPLICADAS**

#### **Inconsistencia Detectada**
```javascript
// ❌ PROBLEMA: Se crean múltiples credentials por user/site
sites[siteId].credentials[credentialId1] = { userId: "user123", ... }
sites[siteId].credentials[credentialId2] = { userId: "user123", ... } // DUPLICADO

// ✅ SOLUCIÓN: Un user = Una credencial por site
sites[siteId].credentials[userId] = { permissions, accessLevel, role }
```

#### **Corrección Requerida**
1. **Auditoría Completa**: Identificar todas las credenciales duplicadas
2. **Consolidación**: Mergear credenciales duplicadas manteniendo permisos máximos
3. **Validación**: Implementar constraint único user-site en credenciales
4. **Roles Correctos**: Asignar roles en documentos de roles, no en credenciales

### **Permisos Granulares**
- **Niveles**: public, site_member, admin, self
- **Recursos**: static/dynamic modules, codeblocks, entities
- **Validación**: GranularPermissionSystem con cache inteligente

## 📁 **ESTRUCTURA DE ARCHIVOS CLAVE**

### **⚠️ DELIMITACIÓN CRÍTICA: PROYECTOR vs LEGACY**

#### **✅ PROYECTOR V1.2 (Arquitectura Actual)**
```
src/proyector/                   # Frontend Proyector v1.2
├── components/                  # Componentes React modernos
├── store/                       # Zustand store unificado
├── pageBlocks/                  # Universal Engine
├── hooks/                       # Hooks personalizados
└── routes/                      # Sistema de rutas

functions-js/proyector/          # Backend Proyector v1.2
├── HyperContext.js              # Capa base abstracción Firebase
├── sites/                       # Gestión de sites
├── specs/                       # Sistema specs dinámicos
└── security/                    # Permisos granulares

public/staticModules/            # Módulos estáticos v1.2
public/staticCodeblocks/         # CodeBlocks estáticos v1.2
public/staticSites/              # Sites de demostración v1.2
```

#### **❌ LEGACY (NO TOCAR - Fuera de Proyector)**
```
src/modules/                     # LEGACY - Sistema antiguo
src/components/                  # LEGACY - Componentes antiguos
src/pages/                       # LEGACY - Páginas antiguas
public/dataInstances/            # LEGACY - Datos antiguos
public/data/                     # LEGACY - Configuraciones antiguas
functions-js/index.js (exports)  # LEGACY - Solo mantener Proyector exports
```

### **Backend Proyector Completo** (`functions-js/proyector/`)
```
├── HyperContext.js              # Capa base abstracción Firebase
├── HyperContextEnhanced.js      # Navegación jerárquica sites/entities
├── SystemInitializer.js        # Inicialización del sistema
├── UserManager.js               # Gestión de usuarios
├── ChatManager.js               # Sistema de chat y conversaciones
├── BotAgentsManager.js          # Gestión de agentes AI
├── BotFlowsOrchestrator.js      # Orquestación de flujos bot
├── BotConfigsManager.js         # Configuraciones de bots
├── AIToolsManager.js            # Herramientas AI integradas
├── UnifiedAIManager.js          # Manager unificado AI
├── ToolsExecutionEngine.js      # Motor ejecución herramientas
├── CodeBlocksManager.js         # Gestión CodeBlocks dinámicos
├── CodeBlocksUnifiedHandler.js  # Handler unificado CodeBlocks
├── ModuleManager.js             # Gestión módulos dinámicos
├── ModulesUnifiedHandler.js     # Handler unificado módulos
├── SitesUnifiedHandler.js       # Handler unificado sites
├── specs/DynamicSpecsManager.js # Gestión specs dinámicos
├── sites/                       # Sistema completo sites
│   ├── SiteCreator.js           # Creación de sites
│   ├── SiteManager.js           # Gestión general sites
│   ├── BrandingManager.js       # Personalización branding
│   ├── ModuleInstaller.js       # Instalación módulos
│   └── TemplateManager.js       # Gestión templates
├── security/                    # Sistema seguridad completo
│   ├── GranularPermissionSystem.js # Sistema permisos
│   ├── RoleManager.js           # Gestión roles
│   ├── PermissionController.js  # Control permisos
│   ├── SecurityAuditLogger.js   # Auditoría seguridad
│   └── ThreatProtectionSystem.js # Protección amenazas
├── communication/               # Sistema comunicación
│   ├── UniversalCommunicationSystem.js # Sistema universal
│   └── EnhancedAIToolsManager.js # Tools AI mejorados
├── codeblocks/                  # Sistema CodeBlocks
│   ├── CodeBlocksInstaller.js   # Instalación CodeBlocks
│   ├── registry/                # Registro CodeBlocks
│   ├── security/                # Seguridad CodeBlocks
│   └── static/                  # Gestión static CodeBlocks
├── ai/                          # Sistema AI completo
│   ├── GenkitManager.js         # Gestión Genkit
│   ├── OptimizedGenkitManager.js # Genkit optimizado
│   └── memory/                  # Sistema memoria AI
├── dynamic/                     # Sistema dinámico
│   ├── DynamicCodeStorage.js    # Storage código dinámico
│   ├── BundleGenerator.js       # Generación bundles
│   ├── LibraryManagerBackend.js # Gestión librerías
│   ├── DynamicEndpointPairSystem.js # Endpoints dinámicos
│   └── PathBasedRouter.js       # Router basado en paths
├── auth/                        # Sistema autenticación
│   ├── AuthContextManager.js    # Contexto autenticación
│   └── index.js                 # Exports autenticación
├── performance/                 # Sistema performance
│   ├── PerformanceTierManager.js # Gestión tiers performance
│   └── BatchPermissionChecker.js # Verificación batch permisos
├── cache/                       # Sistema cache
│   ├── AICacheManager.js        # Cache AI
│   ├── CacheWarmingManager.js   # Calentamiento cache
│   └── PermissionCache.js       # Cache permisos
├── monitoring/                  # Sistema monitoreo
│   ├── PerformanceMonitor.js    # Monitor performance
│   └── PermissionSystemMonitor.js # Monitor permisos
├── storage/                     # Sistema storage
│   ├── StorageStrategy.js       # Estrategias storage
│   └── CodeVersioning.js        # Versionado código
├── testing/                     # Sistema testing
│   ├── ProyectorTestSuite.js    # Suite tests Proyector
│   └── PermissionSystemValidator.js # Validador permisos
├── tools/                       # Herramientas sistema
│   ├── UnifiedToolsRegistry.js  # Registro herramientas
│   └── index.js                 # Exports herramientas
├── utils/                       # Utilidades sistema
│   ├── Logger.js                # Sistema logging
│   ├── PathConstructor.js       # Constructor paths
│   └── ClientIPDetector.js      # Detector IP cliente
└── v1/                          # Handlers v1
    ├── ProyectorV1Handler.js    # Handler principal v1
    └── UnifiedTriggersManager.js # Manager triggers unificado
```

### **Frontend Proyector** (`src/proyector/`)
```
├── libs/HyperContextFrontend.js # Cliente HyperContext
├── store/index.js               # Store Zustand unificado
├── hooks/useHyperContext.js     # Hook principal datos
├── pageBlocks/renderers/UniversalRenderer.jsx # Motor renderizado
├── dynamic/                     # Sistema carga dinámica
│   ├── BundleLoader.js          # Cargador bundles
│   ├── ModuleRegistry.js        # Registro módulos
│   └── LibraryManager.js        # Gestión librerías frontend
└── utils/PathConstructor.js     # Construcción paths
```

## 🔗 **DOCUMENTACIÓN DE REFERENCIA**

### **Documentos Principales v1.2**
- **[README.md](./README.md)** - Índice completo documentación
- **[big-picture-unified-v1.2.md](./big-picture-unified-v1.2.md)** - Arquitectura holística
- **[tree-file-v1.2.md](./tree-file-v1.2.md)** - Estado actual implementación
- **[complete-system-diagrams.md](./complete-system-diagrams.md)** - Diagramas exhaustivos
- **[sites-architecture-detailed.md](./sites-architecture-detailed.md)** - Arquitectura sites
- **[SPECS-CODEBLOCKS-FINAL.md](./SPECS-CODEBLOCKS-FINAL.md)** - Arquitectura specs-codeblocks

### **Arquitectura y Paths**
- **[big-picture/hypercontext-paths-tree.md](./big-picture/hypercontext-paths-tree.md)** - Paths HyperContext
- **[architecture/backend-frontend-separation.md](./architecture/backend-frontend-separation.md)** - Separación backend/frontend

## ⚡ **COMANDOS Y DESARROLLO**

### **Comandos Principales**
```bash
# Desarrollo
npm run dev          # Frontend + Firebase emulators
npm run build        # Build producción
npm run test         # Tests completos

# Firebase
firebase emulators:start  # Solo emulators
firebase deploy          # Deploy producción
```

### **Metodología de Desarrollo**
1. **Investigación exhaustiva** antes de implementación
2. **Lectura completa de archivos** para debugging
3. **Verificación arquitectural** antes de cambios
4. **Actualización documentación** en cada cambio

## 🚀 **SISTEMA DINÁMICO IMPLEMENTADO**

### **Dynamic System Architecture**
```
DYNAMIC SYSTEM = BOTAGENTS GENERATION + HYPERCONTEXT STORAGE + INTELLIGENT CACHING + FRONTEND LOADING
```

### **Flujo Completo Dynamic System**
1. **Usuario**: "Crear módulo inventario" via chat
2. **BotAgents**: Genera código dinámico
3. **HyperContext**: Almacena en `sites[siteId].dynamicModules[]`
4. **Bundle System**: Comprime y optimiza código
5. **Frontend**: Carga y ejecuta dinámicamente

### **Storage Strategy por Entorno**
```javascript
// Development: HyperContext Firestore (raw code)
sites[siteId].dynamicModules[moduleId] = {
  code: "raw_javascript_code",
  metadata: { version, dependencies },
  status: "development"
}

// Staging: HyperContext bundled
sites[siteId].dynamicModules[moduleId] = {
  code: "raw_code",
  bundle: "compressed_bundle",
  status: "staging"
}

// Production: Firebase Storage reference
sites[siteId].cachedBundles[moduleId] = {
  storagePath: "sites/siteId/dynamic-modules/moduleId.gz",
  compressionRatio: 0.25,
  status: "production"
}
```

### **Frontend Dynamic Loading**
- **BundleLoader**: Cache-first strategy con TTL
- **Decompressor**: Browser-native APIs (gzip/brotli)
- **ModuleRegistry**: Namespacing y hot reload
- **ExecutionSlots**: Contextos aislados por tipo
- **LibraryManager**: Resolución automática React/Zustand/etc

## 🔧 **CORRECCIONES CRÍTICAS IMPLEMENTADAS**

### **Separación Backend/Frontend (2025-07-02)**
- ✅ **ProyectorV1Handler.js**: Eliminadas importaciones a `public/staticCodeblocks`
- ✅ **ModuleManager.js**: Reemplazado acceso directo con HyperContext
- ✅ **SystemIndexer.js**: Eliminadas referencias hardcodeadas
- ✅ **Patrón Establecido**: Backend NUNCA importa desde `public/` o `src/`

### **Errores Críticos Resueltos (2025-07-01)**
- ✅ **ChartConfigDataField**: Referencias estáticas corregidas
- ✅ **Store Index**: Export duplicado eliminado
- ✅ **LibraryManager**: Dependencias faltantes instaladas
- ✅ **Ionic Components**: Componentes deprecados removidos
- ✅ **Iconicons**: Iconos faltantes corregidos

### **Migración Modules Completada (2025-06-28)**
- ✅ **Renombrado**: `public/modules/` → `public/staticModules/`
- ✅ **Migrado**: Componentes React → `public/staticCodeblocks/`
- ✅ **Eliminado**: `src/proyector/modules/` completamente
- ✅ **17 Static Modules**: Sistema completo migrado
- ✅ **12 Static CodeBlocks**: Componentes React migrados

## 📊 **MÉTRICAS DEL SISTEMA**

### **Arquitectura Implementada**
- **100%** Separación backend/frontend
- **100%** Arquitectura JSON-driven
- **62** Funcionalidades en 7 slices Zustand
- **50+** Componentes migrados a arquitectura correcta
- **17** Static Modules configurados
- **12** Static CodeBlocks implementados

### **Performance y Escalabilidad**
- **Cache multinivel**: Operaciones, estado, principal
- **Compresión adaptativa**: gzip/brotli por entorno
- **Bundle optimization**: Categorías core/ui/business/site-specific
- **Real-time sync**: Firebase onSnapshot patterns
- **Límite 1MB**: Manejado con subcollections automáticas

## 🎯 **METODOLOGÍA OPERATIVA**

### **"Operativo Completar Checks"**
1. **Investigación exhaustiva** antes de implementación
2. **Lectura completa de archivos** para debugging
3. **Verificación arquitectural** antes de cambios
4. **Validación cross-component** para alineación
5. **Actualización documentación** progresiva

### **Metodología para Tasks/Subtasks Robustos**

#### **Fase 1: Investigación Arquitectural (SI O SI)**
```markdown
1. **Delimitar Scope Proyector v1.2**:
   - ✅ INCLUIR: src/proyector/, public/staticModules/, public/staticCodeblocks/, public/staticSites/, functions-js/proyector/
   - ❌ EXCLUIR: public/dataInstances/, public/data/, src/modules/, src/components/, src/pages/

2. **Investigar Componentes Existentes**:
   - Leer archivos completos relacionados al task
   - Identificar patrones arquitecturales actuales
   - Verificar alineación con Sites-CodeBlocks-Specs
   - Detectar violaciones de hermeticidad en staticCodeblocks

3. **Verificar Reutilización**:
   - Buscar componentes existentes que puedan reutilizarse
   - Evaluar si la solución ya existe parcialmente
   - Identificar gaps específicos a resolver
   - Priorizar staticCodeblocks sobre controllers hardcodeados
```

#### **Fase 1.5: Validación de Hermeticidad (CRÍTICO)**
```markdown
1. **Auditoría de Imports**:
   - Verificar que staticCodeblocks NO importen desde src/proyector/
   - Identificar violaciones de hermeticidad existentes
   - Documentar patrones de inyección de dependencias requeridos

2. **Validación Arquitectural**:
   - Confirmar separación backend/frontend estricta
   - Verificar que no hay controllers hardcodeados cuando deben ser staticCodeblocks
   - Asegurar que todo chat/menu/bot está basado en PageBlocks

3. **Planificación de Correcciones**:
   - Priorizar corrección de violaciones críticas
   - Planificar migración de controllers a staticCodeblocks
   - Definir patrón de inyección de dependencias
```

#### **Fase 2: Planificación Detallada**
```markdown
1. **Definir Objetivo Específico**:
   - Resultado esperado concreto y medible
   - Criterios de éxito claros
   - Impacto en arquitectura general

2. **Diseñar Solución Alineada**:
   - Usar patrones Proyector v1.2 existentes
   - Mantener separación backend/frontend estricta
   - Aplicar principio Sites-CodeBlocks-Specs

3. **Identificar Archivos a Modificar**:
   - Lista específica de archivos Proyector v1.2
   - Orden de modificación (dependencias)
   - Puntos de integración críticos
```

#### **Fase 3: Implementación Progresiva**
```markdown
1. **Implementar por Componentes**:
   - Un componente a la vez
   - Verificar funcionamiento antes de continuar
   - Mantener coherencia arquitectural

2. **Validar Integración**:
   - Probar interacción entre componentes
   - Verificar no romper funcionalidad existente
   - Confirmar alineación con big-picture

3. **Actualizar Documentación**:
   - Registrar cambios en tree-file-v1.2.md
   - Actualizar diagramas si aplica
   - Documentar nuevos patrones creados
```

#### **Fase 4: Verificación y Checks**
```markdown
1. **Verificación Técnica**:
   - Funcionamiento correcto de la implementación
   - No errores de sintaxis o runtime
   - Performance adecuado

2. **Verificación Arquitectural**:
   - Alineación con principios Proyector v1.2
   - Separación backend/frontend mantenida
   - Patrones Sites-CodeBlocks-Specs respetados

3. **Marcar Task Completo**:
   - Solo cuando TODOS los criterios se cumplen
   - Documentación actualizada
   - Integración verificada
```

### **Principios de Desarrollo**
- **SI O SI**: Verificación exhaustiva antes de implementar
- **Frontend-First**: HyperContextFrontend para todas las operaciones
- **JSON-Driven**: DataFields, FieldSets, template.json para UI
- **Separación Estricta**: Controllers puros + Templates JSON
- **Sites-Specific**: Todo es site-specific, 'main' = global
- **Solo Proyector v1.2**: NUNCA tocar archivos legacy (dataInstances, data, src/modules)
- **Hermeticidad Total**: StaticCodeblocks NUNCA importan desde src/proyector/
- **PageBlocks First**: Chat, menus, bots deben ser staticCodeblocks, NO controllers hardcodeados
- **Inyección de Dependencias**: Store, auth, renderers se pasan via props
- **Backend Scope Completo**: Incluir functions-js/proyector/ en delimitación arquitectural

## 🔧 **PLAN DE CORRECCIÓN ARQUITECTURAL**

### **Fase 1: Corrección de Hermeticidad (CRÍTICO)**

#### **Violaciones Identificadas para Corrección**
```javascript
// 1. message-text-block/component.jsx
// ❌ ELIMINAR: import { useAuth } from '../../../src/proyector/store';
// ✅ CAMBIAR: export const MessageTextBlock = ({ user, onAction, ... }) => {

// 2. message-pageblocks-block/component.jsx
// ❌ ELIMINAR: import { useAuth } from '../../../src/proyector/store';
// ❌ ELIMINAR: import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';
// ✅ CAMBIAR: export const MessagePageblocksBlock = ({ user, UniversalRenderer, ... }) => {

// 3. chat-track-layout/component.jsx
// ❌ ELIMINAR: import { useAuth, useSite } from '../../../src/proyector/store';
// ❌ ELIMINAR: import { UniversalRenderer } from '../../../src/proyector/pageBlocks/renderers/UniversalRenderer';
// ✅ CAMBIAR: export const ChatTrackLayout = ({ user, currentSite, UniversalRenderer, ... }) => {
```

#### **Patrón de Corrección Estándar**
```javascript
// ✅ PATRÓN CORRECTO: UniversalRenderer inyecta dependencias
// En src/proyector/pageBlocks/renderers/UniversalRenderer.jsx
const renderStaticCodeblock = (codeblockConfig, props) => {
  const Component = loadStaticCodeblock(codeblockConfig.slug);

  // ✅ INYECCIÓN DE DEPENDENCIAS
  const injectedProps = {
    ...props,
    user: useAuth().user,           // Inyectar auth
    currentSite: useSite().current, // Inyectar site
    store: useProyectorStore(),     // Inyectar store si necesario
    UniversalRenderer: UniversalRenderer, // Inyectar renderer para recursión
    onAction: handleAction,         // Inyectar callbacks
  };

  return <Component {...injectedProps} />;
};
```

### **Fase 2: Migración Controllers a StaticCodeblocks**

#### **Controllers a Eliminar/Migrar**
```javascript
// ❌ ELIMINAR: Controllers hardcodeados
src/proyector/pageBlocks/controllers/lateralChatController.js
src/components/chat/LateralChatInterface.jsx
src/components/chat/ChatIntegration.jsx

// ✅ MIGRAR A: StaticCodeblocks
public/staticCodeblocks/lateral-chat-interface/
public/staticCodeblocks/chat-integration/
public/staticCodeblocks/menu-top-navigation/
public/staticCodeblocks/menu-lateral-navigation/
public/staticCodeblocks/bot-agents-panel/
public/staticCodeblocks/bot-flows-editor/
public/staticCodeblocks/bot-configs-manager/
```

### **Fase 3: Sistema Chat Robusto Completo**

#### **Funcionalidades Chat Requeridas**
```javascript
// ✅ IMPLEMENTAR: Chat robusto con tools completos
public/staticCodeblocks/chat-tools-panel/
├── codeblock.json    # Panel herramientas AI
├── component.jsx     # Interface tools
└── styles.css        # Estilos panel

public/staticCodeblocks/chat-context-manager/
├── codeblock.json    # Gestión contexto chat
├── component.jsx     # Interface contexto
└── styles.css        # Estilos contexto

public/staticCodeblocks/chat-history-viewer/
├── codeblock.json    # Visor historial
├── component.jsx     # Interface historial
└── styles.css        # Estilos historial
```

### **Fase 4: Validación y Testing**

#### **Scripts de Validación Requeridos**
```javascript
// ✅ CREAR: Script validación hermeticidad
docs/Proyector/v1.2/scripts/validate-hermeticidad.js
// - Escanear todos los staticCodeblocks
// - Detectar imports desde src/proyector/
// - Reportar violaciones y sugerir correcciones

// ✅ CREAR: Script testing chat completo
docs/Proyector/v1.2/testing/chat-system-integration-test.js
// - Probar flujo completo chat con PageBlocks
// - Validar herramientas AI integradas
// - Verificar real-time sync
```

#### **Criterios de Éxito**
- ✅ **Hermeticidad 100%**: Cero imports desde src/proyector/ en staticCodeblocks
- ✅ **Chat Robusto**: Funcionalidades completas con tools integrados
- ✅ **PageBlocks First**: Todo chat/menu/bot basado en staticCodeblocks
- ✅ **Credenciales Únicas**: Un user = una credencial por site
- ✅ **Backend Scope**: Documentación completa functions-js/proyector/

## 🔮 **PRÓXIMOS PASOS ARQUITECTURALES**

### **Fase Actual: Corrección Arquitectural Crítica**
- **Hermeticidad Total**: Eliminar todas las violaciones de imports
- **Chat System Robusto**: Implementación completa con tools integrados
- **Controllers Migration**: Migrar todos los controllers hardcodeados a staticCodeblocks
- **Credentials Cleanup**: Resolver duplicación de credenciales

### **Fase Siguiente: Dynamic System Integration**
- **BotAgents Code Generation**: Sistema generación via chat
- **Intelligent Caching**: Bundling y compresión producción
- **Dynamic Permissions**: Permisos específicos recursos dinámicos
- **Marketplace Integration**: Compartir CodeBlocks entre usuarios

### **Visión v1.3**
- **Multi-tenant Global**: Millones de sites simultáneos
- **AI Autónomo**: Agentes que crean sites automáticamente
- **Blockchain Integration**: NFTs, smart contracts, Web3
- **Metaverse Ready**: Sites en realidad virtual/aumentada

## 📋 **CHECKLIST DE VALIDACIÓN ARQUITECTURAL**

### **Antes de Implementar Cualquier Task**
- [ ] **Scope Delimitado**: ¿Está dentro de Proyector v1.2? (src/proyector/, public/static*, functions-js/proyector/)
- [ ] **Hermeticidad Verificada**: ¿Los staticCodeblocks NO importan desde src/proyector/?
- [ ] **PageBlocks First**: ¿Se usa staticCodeblocks en lugar de controllers hardcodeados?
- [ ] **Inyección de Dependencias**: ¿Se pasan store/auth/renderers via props?
- [ ] **Backend Separation**: ¿No hay imports cruzados backend/frontend?

### **Durante la Implementación**
- [ ] **Patrón Consistente**: ¿Se sigue el patrón de otros staticCodeblocks?
- [ ] **Dependencias Declaradas**: ¿Están en codeblock.json las librerías usadas?
- [ ] **Props Interface**: ¿Está bien definida la interface de props?
- [ ] **Error Handling**: ¿Se manejan errores gracefully?
- [ ] **Performance**: ¿Se evitan re-renders innecesarios?

### **Después de Implementar**
- [ ] **Testing Completo**: ¿Funciona en todos los escenarios?
- [ ] **Documentación Actualizada**: ¿Se actualizó tree-file-v1.2.md?
- [ ] **No Regresiones**: ¿No se rompió funcionalidad existente?
- [ ] **Integración Verificada**: ¿Se integra correctamente con otros componentes?
- [ ] **Hermeticidad Mantenida**: ¿Se mantiene la hermeticidad total?

---

**Este documento sirve como referencia completa para agentes AI trabajando con el Proyector v1.2. Debe leerse al inicio de cada conversación nueva para entender la arquitectura completa del sistema.**

**CRÍTICO**: Antes de cualquier implementación, verificar hermeticidad de staticCodeblocks y usar inyección de dependencias via props. El sistema debe mantener separación backend/frontend estricta y usar PageBlocks para toda funcionalidad de UI.
