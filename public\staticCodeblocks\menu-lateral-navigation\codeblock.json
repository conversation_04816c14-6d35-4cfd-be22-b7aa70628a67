{"id": "menu-lateral-navigation", "name": "Menu Lateral Navigation", "version": "1.0.0", "description": "Sistema de menú lateral coordinado con MenuTop, migrado de ProyectorMenu.js con navegación dinámica desde site.json", "type": "navigation", "category": "ui", "author": "Proyector Team", "tags": ["menu", "navigation", "lateral", "sidebar", "site", "legacy-design"], "dependencies": {"slices": ["uiSlice", "authSlice", "siteSlice", "moduleSlice"], "libraries": ["@ionic/react", "ionicons", "framer-motion"], "staticCodeblocks": ["menu-top-navigation"], "legacy": ["useBrandingColors", "useProyectorNavigation", "menuController"]}, "permissions": {"required": ["ui:access", "site:read"], "optional": ["admin:access", "modules:manage"]}, "configuration": {"enableDynamicNavigation": true, "enableModuleNavigation": true, "enableCollapsibleGroups": true, "enableBranding": true, "enableAnimations": true, "defaultCollapsed": false, "autoCloseOnNavigate": true, "responsiveBreakpoint": "lg"}, "navigation": {"source": "site.json", "path": "config.navigation.sideMenu.groups", "fallback": "navigation.groups", "moduleGeneration": true, "permissionFiltering": true, "iconMapping": {"home": "home-outline", "dashboard": "speedometer-outline", "sites": "layers-outline", "users": "people-outline", "settings": "settings-outline", "stats": "stats-chart-outline"}}, "props": {"required": ["currentSite", "modules", "user", "history", "useBrandingColors", "useProyectorNavigation", "menuController"], "optional": ["className", "enableHeader", "enableFooter", "onNavigate", "onGroupToggle", "triggerId"]}, "state": {"local": ["expandedGroups", "isMenuOpen", "selectedItem", "navigationConfig"], "injected": ["sidebarCollapsed", "menuTopMode", "currentRoute", "userAuth"]}, "events": {"emits": ["navigation_changed", "group_toggled", "menu_opened", "menu_closed", "item_selected"], "listens": ["site_changed", "modules_changed", "route_changed", "menu_top_mode_changed"]}, "integration": {"menuTopNavigation": {"coordination": true, "hideWhenHarmonyMode": true, "showWhenSiteMode": true, "hideWhenUserMode": true}, "uiSlice": {"methods": ["setSidebarCollapsed", "setCurrentRoute"], "events": ["ui_sidebar_toggled", "ui_route_changed"]}, "siteSlice": {"methods": ["getCurrentSite", "getSiteNavigation"], "events": ["site_changed", "site_navigation_updated"]}, "moduleSlice": {"methods": ["getEnabledModules", "getModuleNavigation"], "events": ["modules_changed"]}}, "legacyCompatibility": {"ProyectorMenu": {"migrated": true, "features": ["dynamicNavigation", "moduleGeneration", "branding", "collapsibleGroups"]}, "siteJson": {"structure": "config.navigation.sideMenu.groups", "fallback": "navigation.groups", "permissions": "granular"}, "branding": {"system": "useBrandingColors", "dynamicColors": true, "customCSS": true}, "icons": {"ionicons": true, "fontAwesome": true, "dynamic": true}}, "responsive": {"mobile": {"autoCollapse": true, "overlayMode": true, "swipeGestures": true}, "tablet": {"pushMode": true, "partialCollapse": true}, "desktop": {"splitPane": true, "persistentVisible": true, "hoverExpand": true}}, "coordination": {"menuTop": {"hideOnHarmonyMode": true, "showOnSiteMode": true, "hideOnUserMode": true, "syncSelection": true}, "chat": {"pushContent": true, "respectChatVisible": true}, "panelDrawer": {"avoidOverlap": true, "coordinateZIndex": true}}, "metadata": {"createdAt": "2024-07-03T00:00:00Z", "migratedFrom": "src/proyector/components/common/ProyectorMenu.js", "migrationReason": "Migración de menú lateral legacy con coordinación MenuTop y navegación dinámica", "architecture": "PageBlocks + StaticCodeblocks + Legacy Design", "hermeticity": "total"}}