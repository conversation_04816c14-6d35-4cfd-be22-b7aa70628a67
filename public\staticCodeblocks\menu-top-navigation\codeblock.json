{"id": "menu-top-navigation", "name": "Menu Top Navigation", "version": "1.0.0", "description": "Sistema de menú top con 3 modos conciliando diseño legacy refinado: Harmony (chat con botAgents), Current Site (navegación lateral), User's Sections (datos personales)", "type": "navigation", "category": "ui", "author": "Proyector Team", "tags": ["menu", "navigation", "top", "harmony", "chat", "site", "legacy-design"], "dependencies": {"slices": ["uiSlice", "authSlice", "siteSlice", "chatSlice"], "libraries": ["@ionic/react", "ionicons", "framer-motion", "simplebar-react"], "staticCodeblocks": ["lateral-chat-interface", "site-navigation", "user-sections"], "legacy": ["getMenuPages", "useBrandingColors", "permissions"]}, "permissions": {"required": ["ui:access", "site:read"], "optional": ["chat:access", "admin:access", "user:manage"]}, "configuration": {"defaultMode": "site", "enableModeToggle": true, "enableExpansion": true, "enableAnimations": true, "autoCollapseOnNavigate": true, "persistMode": true, "responsiveBreakpoint": "lg", "legacyDesign": {"topMenuWidth": "64px", "enableRippleEffect": true, "enableHoverStates": true, "enableBrandingColors": true, "accordionStyle": "inset"}}, "modes": {"harmony": {"id": "harmony", "title": "Harmony", "subtitle": "Chat con botAgents", "icon": "chatbubbles-outline", "iconActive": "chatbubbles", "color": "primary", "hidesSideMenu": true, "component": "lateral-chat-interface", "description": "Asistente inteligente con herramientas", "permissions": ["chat:access"], "features": ["botAgents", "tools", "memory", "semanticSearch"], "legacyMenuName": "user", "sectionKey": "harmony"}, "site": {"id": "site", "title": "Current Site", "subtitle": "Navegación del site", "icon": "layers-outline", "iconActive": "layers", "color": "secondary", "showsSideMenu": true, "component": "site-navigation", "description": "Secciones y módulos del site", "permissions": ["site:read"], "features": ["navigation", "modules", "pages"], "legacyMenuName": "instance", "sectionKey": "instance"}, "user": {"id": "user", "title": "User's Sections", "subtitle": "Datos personales", "icon": "person-outline", "iconActive": "person", "color": "tertiary", "hidesSideMenu": true, "component": "user-sections", "description": "Perfil, sites y configuración", "permissions": ["user:read"], "features": ["profile", "sites", "settings", "preferences"], "legacyMenuName": "user", "sectionKey": "user"}}, "props": {"required": ["user", "currentSite", "selectedInstance", "instance", "Universal<PERSON><PERSON><PERSON>", "history", "getMenuPages", "useBrandingColors", "isAllowed", "attachPrefix"], "optional": ["defaultMode", "enabledModes", "className", "onModeChange", "onNavigate", "onExpand", "onCollapse", "triggerId", "menuName"]}, "state": {"local": ["currentMode", "isExpanded", "selectedSection", "hoveredIndex", "openAccordion", "isTransitioning"], "injected": ["menuTopMode", "sidebarCollapsed", "chatVisible", "layout", "userAuth", "location"]}, "events": {"emits": ["mode_changed", "menu_expanded", "menu_collapsed", "navigation_triggered", "sidebar_toggled", "section_selected", "accordion_changed"], "listens": ["site_changed", "user_changed", "layout_changed", "route_changed", "instance_changed"]}, "integration": {"uiSlice": {"methods": ["setMenuTopMode", "setChatVisible", "setSidebarCollapsed", "layout.setMenuExpanded"], "events": ["ui_mode_changed", "ui_sidebar_toggled"]}, "chatSlice": {"methods": ["toggleChatVisible"], "events": ["chat_visibility_changed"]}, "siteSlice": {"methods": ["getCurrentSite"], "events": ["site_changed"]}, "legacy": {"getMenuPages": {"menuNames": ["top", "instance", "user", "panel"], "params": ["menuName", "userAuth", "selectedInstance", "instance", "isAllowed"]}, "permissions": {"system": "legacy", "method": "isAllowed", "params": ["resource", "actions"]}}}, "responsive": {"mobile": {"autoCollapse": true, "fullWidth": true, "stackModes": true, "topMenuWidth": "56px"}, "tablet": {"showModeIcons": true, "compactMode": true, "topMenuWidth": "64px"}, "desktop": {"showFullLabels": true, "enableHover": true, "showDescriptions": true, "topMenuWidth": "64px"}}, "legacyCompatibility": {"MenuMainContent": {"migrated": true, "features": ["TopMenu", "MenuOfSections", "permissions", "branding"]}, "MenuOfSections": {"migrated": true, "features": ["accordions", "navigation", "permissions", "icons"]}, "branding": {"system": "useBrandingColors", "classes": ["bg-brand-primary-tint", "text-brand-medium-contrast", "bg-brand-medium-tint"]}, "permissions": {"system": "legacy", "granular": true, "resources": ["panel", "usersSections", "instance", "chat"]}}, "metadata": {"createdAt": "2024-07-03T00:00:00Z", "migratedFrom": ["src/proyector/components/common/MenuTop.js", "src/modules/panel/MenuMainContent.js", "src/modules/panel/MenuOfSections.js"], "migrationReason": "Conciliación de diseño legacy refinado con arquitectura staticCodeblocks hermética", "architecture": "PageBlocks + StaticCodeblocks + Legacy Design", "hermeticity": "total"}}