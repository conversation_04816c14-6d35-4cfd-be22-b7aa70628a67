{"id": "menu-top-navigation", "name": "Menu Top Navigation", "version": "1.0.0", "description": "Sistema de menú top con 3 modos: Harmony (chat con botAgents), Current Site (navegación lateral), User's Sections (datos personales)", "type": "navigation", "category": "ui", "author": "Proyector Team", "tags": ["menu", "navigation", "top", "harmony", "chat", "site"], "dependencies": {"slices": ["uiSlice", "authSlice", "siteSlice", "chatSlice"], "libraries": ["@ionic/react", "ionicons", "framer-motion"], "staticCodeblocks": ["chat-system", "site-navigation", "user-sections"]}, "permissions": {"required": ["ui:access", "site:read"], "optional": ["chat:access", "admin:access", "user:manage"]}, "configuration": {"defaultMode": "site", "enableModeToggle": true, "enableExpansion": true, "enableAnimations": true, "autoCollapseOnNavigate": true, "persistMode": true, "responsiveBreakpoint": "lg"}, "modes": {"harmony": {"id": "harmony", "title": "Harmony", "subtitle": "Chat con botAgents", "icon": "chatbubbles-outline", "iconActive": "chatbubbles", "color": "primary", "hidesSideMenu": true, "component": "chat-system", "description": "Asistente inteligente con herramientas", "permissions": ["chat:access"], "features": ["botAgents", "tools", "memory", "semanticSearch"]}, "site": {"id": "site", "title": "Current Site", "subtitle": "Navegación del site", "icon": "layers-outline", "iconActive": "layers", "color": "secondary", "showsSideMenu": true, "component": "site-navigation", "description": "Secciones y módulos del site", "permissions": ["site:read"], "features": ["navigation", "modules", "pages"]}, "user": {"id": "user", "title": "User's Sections", "subtitle": "Datos personales", "icon": "person-outline", "iconActive": "person", "color": "tertiary", "hidesSideMenu": true, "component": "user-sections", "description": "Perfil, sites y configuración", "permissions": ["user:read"], "features": ["profile", "sites", "settings", "preferences"]}}, "props": {"required": ["user", "currentSite", "Universal<PERSON><PERSON><PERSON>", "history"], "optional": ["defaultMode", "enabledModes", "className", "onModeChange", "onNavigate", "onExpand", "onCollapse"]}, "state": {"local": ["currentMode", "isExpanded", "isTransitioning", "lastMode"], "injected": ["menuTopMode", "sidebarCollapsed", "chatVisible", "layout"]}, "events": {"emits": ["mode_changed", "menu_expanded", "menu_collapsed", "navigation_triggered", "sidebar_toggled"], "listens": ["site_changed", "user_changed", "layout_changed", "route_changed"]}, "integration": {"uiSlice": {"methods": ["setMenuTopMode", "setChatVisible", "setSidebarCollapsed", "layout.setMenuExpanded"], "events": ["ui_mode_changed", "ui_sidebar_toggled"]}, "chatSlice": {"methods": ["toggleChatVisible"], "events": ["chat_visibility_changed"]}, "siteSlice": {"methods": ["getCurrentSite"], "events": ["site_changed"]}}, "responsive": {"mobile": {"autoCollapse": true, "fullWidth": true, "stackModes": true}, "tablet": {"showModeIcons": true, "compactMode": true}, "desktop": {"showFullLabels": true, "enableHover": true, "showDescriptions": true}}, "metadata": {"createdAt": "2024-07-03T00:00:00Z", "migratedFrom": "src/proyector/components/common/MenuTop.js", "migrationReason": "Migración de component hardcodeado a staticCodeblock hermético", "architecture": "PageBlocks + StaticCodeblocks", "hermeticity": "total"}}