/**
 * MenuTopNavigation Component - Sistema de Menú Top con 3 Modos
 * 
 * Concilia el diseño legacy refinado con la nueva arquitectura staticCodeblocks:
 * - Modo Harmony: Chat con botAgents (lateral-chat-interface)
 * - Modo Current Site: Navegación lateral con secciones del site actual
 * - Modo User's Sections: Datos personales, mis sites, configuraciones
 * 
 * Migrado desde:
 * - src/proyector/components/common/MenuTop.js
 * - src/modules/panel/MenuMainContent.js  
 * - src/modules/panel/MenuOfSections.js
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  IonHeader,
  IonToolbar,
  IonButtons,
  IonButton,
  IonIcon,
  IonTitle,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonChip,
  IonNote,
  IonContent,
  IonAccordion,
  IonAccordionGroup,
  IonItem,
  IonList,
  IonMenuToggle,
  IonRippleEffect
} from '@ionic/react';

// Icons
import {
  chatbubbles,
  chatbubblesOutline,
  layers,
  layersOutline,
  person,
  personOutline,
  menuOutline,
  closeOutline,
  chevronDownOutline,
  chevronForwardOutline
} from 'ionicons/icons';

// SimpleBar para scroll personalizado
import SimpleBar from 'simplebar-react';

/**
 * Hook para verificar si una página está seleccionada (migrado de MenuMainContent)
 */
const useMenuIsSelected = ({ location, attachPrefix }) => {
  const checkIsCurrentPage = (page) => {
    // Para ir a la página frontal
    if (page?.url === '/') { 
      return false;
    }
    // Para menú de secciones top
    if (page?.sectionKey) {
      // Lógica para verificar secciones anidadas
      return location.pathname.includes(page.sectionKey);
    }
    if (page && !page.url && !page.sectionKey) {
      return true;
    }
    return location.pathname.includes(attachPrefix(page.url));
  };

  return { checkIsCurrentPage };
};

/**
 * TopMenu Component - Menú vertical con iconos (migrado de MenuMainContent)
 */
const TopMenu = ({ 
  pages, 
  selectedSection, 
  onSelectSection, 
  className,
  useBrandingColors,
  checkIsCurrentPage 
}) => {
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const { getColor } = useBrandingColors();

  const getItemClass = (page, index, isCurrent) => {
    if (isCurrent) {
      return 'bg-brand-primary-tint';
    }
    if (hoveredIndex === index || selectedSection === page) {
      return 'bg-brand-medium-tint';
    }
    return '';
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      {pages.map((page, index) => {
        let isCurrent = !!checkIsCurrentPage(page);
        let faIcon = isCurrent ? page.faIconAlt : page.faIcon;
        return (
          <div
            key={index}
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
            onClick={() => onSelectSection(page)}
            className="ripple-parent flex flex-col items-center w-full cursor-pointer transition-all duration-200"
          >
            <div className={`
              flex justify-center items-center p-2 rounded-lg mb-1 
              ${getItemClass(page, index, isCurrent)}
            `}> 
              {(page.ionIconAlt || page.ionIcon) ? (
                <IonIcon 
                  icon={isCurrent && page.ionIconAlt ? page.ionIconAlt : page.ionIcon} 
                  className={`text-3xl ${isCurrent ? 'text-brand-medium-contrast' : 'text-brand-medium-contrast'}`}
                />
              ) : null}
              {(page.faIconAlt || page.faIcon) ? (
                ((isCurrent && page.faIconAlt) ? page.faIconAlt : page.faIcon)?.({
                  className: `text-3xl ${isCurrent ? 'text-brand-medium-contrast' : 'text-brand-medium-contrast'}`
                })
              ) : null}
            </div>
            <span className="text-center text-[11px] text-brand-medium-contrast">{page.title}</span>
            <IonRippleEffect />
          </div>
        );
      })}
    </div>
  );
};

/**
 * MenuOfSections Component - Navegación con acordeones (migrado de MenuOfSections)
 */
const MenuOfSections = ({ 
  selectedSection, 
  getMenuPages,
  userAuth,
  selectedInstance,
  instance,
  isAllowed,
  attachPrefix,
  location,
  openAccordion,
  setOpenAccordion
}) => {
  const { checkIsCurrentPage } = useMenuIsSelected({ location, attachPrefix });

  const sectionsList = useMemo(() => {
    if (selectedInstance && userAuth?.userDoc) {
      return getMenuPages({ 
        menuName: selectedSection?.sectionKey || selectedSection?.legacyMenuName, 
        userAuth, 
        isAllowed, 
        selectedInstance, 
        instance 
      });
    }
    return [];
  }, [selectedSection, userAuth?.userDoc, selectedInstance?.id]);

  useEffect(() => {
    if (selectedSection?.sectionKey) {
      let sectionsMenuSpecs = getMenuPages({ 
        menuName: selectedSection.sectionKey, 
        userAuth, 
        isAllowed, 
        selectedInstance, 
        instance 
      });
      let currentParentSection = sectionsMenuSpecs?.find(section => {
        if (section?.items) {
          return section.items.find(item => {
            let isCurrent = checkIsCurrentPage(item);
            if (isCurrent) {
              return section;
            }
          });
        }
      });
      if (currentParentSection) {
        setOpenAccordion(currentParentSection.tabKey);
      }
    }
  }, [selectedSection, location]);

  const handleAccordionChange = (value) => {
    setOpenAccordion(value);
  };

  if (!selectedInstance) {
    return (
      <div className="flex px-4 py-2 place-content-start items-center font-brand-main">
        <div className="text-brand-dark">Cargando...</div>
      </div>
    );
  }

  return (
    <IonAccordionGroup
      expand="inset"
      className="mx-1 !shadow-none"
      value={openAccordion}
      onIonChange={(e) => handleAccordionChange(e.detail.value)}
    >
      {sectionsList?.map((section, index) => {
        let sectionItems = section.items?.filter((item) => (
          item.permissions
          ? isAllowed(item.permissions?.resource, item.permissions?.actions)
          : true
        ));
        return (
          (section?.permissions
            ? isAllowed(section?.permissions?.resource, section?.permissions?.actions)
            : true
          ) ? (
            // sub menu items
            section.tabKey ? (
              (
                sectionItems?.length
              ) ? (
                <IonAccordion value={section.tabKey} key={index} className="!shadow-none">
                  <IonItem slot="header" lines="none">
                    {section.ionIcon ? (
                      <IonIcon icon={section.ionIcon} color="light" className="mr-2" />
                    ) : null}
                    {section.faIcon ? (
                      <div className="py-3 pr-2">
                        <section.faIcon className="text-brand-light text-xl" />
                      </div>
                    ) : null}
                    <IonLabel className="">{section.title}</IonLabel>
                  </IonItem>
                  <div className="" slot="content">
                    {/* extra section items */}
                    {sectionItems?.map((item, index) => (
                      <IonMenuToggle key={index} autoHide={false}>
                        <IonItem
                          routerLink={attachPrefix(item.url)}
                          className={location.pathname.includes(attachPrefix(item.url)) ? 'selected' : ''}
                        >
                          {item.ionIcon ? (
                            <IonIcon icon={item.ionIcon} size="small" className="mr-2 text-brand-light" />
                          ) : null}
                          {item.faIcon ? (
                            <div className="py-3 pr-2">
                              <item.faIcon className="text-brand-light text-xl" />
                            </div>
                          ) : null}
                          <IonLabel>
                            {item.title}
                          </IonLabel>
                        </IonItem>
                      </IonMenuToggle>
                    ))}
                  </div>
                </IonAccordion>
              ) : null
            )
            // single menu item
            : (
              <IonMenuToggle key={index} autoHide={false}>
                <IonItem
                  routerLink={attachPrefix(section.url)}
                  className={section.url === '/' ? '' : location.pathname.includes(attachPrefix(section.url)) ? 'selected' : ''}
                  lines="none"
                  detail={false}
                >
                  {section.ionIcon ? (
                    <IonIcon icon={section.ionIcon} color="light" />
                  ) : null}
                  {section.faIcon ? (
                    <div className="py-3 pr-2">
                      <section.faIcon className="text-brand-light text-xl" />
                    </div>
                  ) : null}
                  <IonLabel className="ml-2">{section.title}</IonLabel>
                </IonItem>
              </IonMenuToggle>
            )
          ) : null
        );
      })}
    </IonAccordionGroup>
  );
};

/**
 * MenuTopNavigation Component Principal
 */
export function MenuTopNavigationController({ 
  // Props requeridas
  user, 
  currentSite, 
  selectedInstance,
  instance,
  UniversalRenderer, 
  history,
  getMenuPages,
  useBrandingColors,
  isAllowed,
  attachPrefix,
  
  // Props del store inyectadas
  menuTopMode,
  sidebarCollapsed,
  chatVisible,
  layout,
  userAuth,
  location,
  
  // Métodos del store inyectados
  setMenuTopMode,
  setChatVisible,
  setSidebarCollapsed,
  
  // Props opcionales
  defaultMode = 'site',
  className = '',
  onModeChange,
  onNavigate,
  onExpand,
  onCollapse,
  triggerId,
  menuName
}) {
  // Estado local
  const [currentMode, setCurrentMode] = useState(menuTopMode || defaultMode);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedSection, setSelectedSection] = useState();
  const [openAccordion, setOpenAccordion] = useState();
  
  // Refs
  const topMenuContentRef = useRef(null);
  
  // Hooks
  const { checkIsCurrentPage } = useMenuIsSelected({ location, attachPrefix });

  // Configuración de modos (conciliando legacy con nuevo diseño)
  const modes = {
    harmony: {
      id: 'harmony',
      title: 'Harmony',
      subtitle: 'Chat con botAgents',
      icon: chatbubblesOutline,
      iconActive: chatbubbles,
      color: 'primary',
      hidesSideMenu: true,
      component: 'lateral-chat-interface',
      description: 'Asistente inteligente con herramientas',
      legacyMenuName: 'user',
      sectionKey: 'harmony'
    },
    site: {
      id: 'site',
      title: currentSite?.displayName || currentSite?.data?.name || 'Main Site',
      subtitle: 'Navegación del site',
      icon: layersOutline,
      iconActive: layers,
      color: 'secondary',
      showsSideMenu: true,
      component: 'site-navigation',
      description: 'Secciones y módulos del site',
      legacyMenuName: 'instance',
      sectionKey: 'instance'
    },
    user: {
      id: 'user',
      title: user?.displayName || 'User',
      subtitle: 'Datos personales',
      icon: personOutline,
      iconActive: person,
      color: 'tertiary',
      hidesSideMenu: true,
      component: 'user-sections',
      description: 'Perfil, sites y configuración',
      legacyMenuName: 'user',
      sectionKey: 'user'
    }
  };

  // Obtener modo actual
  const activeMode = modes[currentMode];

  // Obtener menú top sections (migrado de MenuMainContent)
  const topSectionsMenuSpecs = useMemo(() => {
    if (selectedInstance && userAuth?.userDoc) {
      return getMenuPages({ 
        menuName: 'top', 
        userAuth, 
        isAllowed, 
        selectedInstance, 
        instance 
      });
    }
    return [];
  }, [userAuth?.userDoc, instance, selectedInstance?.id]);

  // Manejar cambio de modo
  const handleModeChange = (modeId) => {
    const mode = modes[modeId];
    if (!mode) return;

    // Actualizar estado local
    setCurrentMode(modeId);
    
    // Actualizar store
    setMenuTopMode(modeId);

    // Manejar visibilidad del sidebar según el modo
    if (mode.hidesSideMenu) {
      setSidebarCollapsed(true);
    } else if (mode.showsSideMenu) {
      setSidebarCollapsed(false);
    }

    // Manejar chat específicamente para modo Harmony
    if (modeId === 'harmony') {
      setChatVisible(true);
      setIsExpanded(true);
    } else {
      // En otros modos, mantener estado actual del chat
      setIsExpanded(false);
    }

    // Callback opcional
    onModeChange?.(modeId, mode);
  };

  // Manejar selección de sección (migrado de MenuMainContent)
  const handleSelectSection = (section) => {
    setSelectedSection(section);
    if (section.url) {
      history.push(section.url);
    }
    onNavigate?.();
  };

  // Toggle expansión del menú top
  const handleToggleExpansion = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    
    if (newExpanded) {
      onExpand?.();
    } else {
      onCollapse?.();
    }
  };

  // Efecto para detectar página actual y seleccionar sección (migrado de MenuMainContent)
  useEffect(() => {
    topSectionsMenuSpecs.find((page, index) => {
      let isCurrent = !!checkIsCurrentPage(page);
      if (isCurrent) {
        setSelectedSection(page);
        return page;
      }
    });
  }, [location, topSectionsMenuSpecs]);

  return (
    <div className={`menu-top ${isExpanded ? 'expanded' : 'collapsed'} ${className}`}>
      {/* Header del MenuTop */}
      <IonHeader className="menu-top-header">
        <IonToolbar>
          {/* Selector de modos */}
          <IonSegment
            value={currentMode}
            onIonChange={(e) => handleModeChange(e.detail.value)}
            className="menu-top-segment"
          >
            {Object.values(modes).map((mode) => (
              <IonSegmentButton key={mode.id} value={mode.id}>
                <IonIcon icon={currentMode === mode.id ? mode.iconActive : mode.icon} />
                <IonLabel>{mode.title}</IonLabel>
              </IonSegmentButton>
            ))}
          </IonSegment>

          {/* Controles del header */}
          <IonButtons slot="end">
            {/* Información del modo actual */}
            <IonChip color={activeMode.color} outline>
              <IonIcon icon={activeMode.iconActive} />
              <IonLabel>{activeMode.description}</IonLabel>
            </IonChip>

            {/* Toggle expansión */}
            <IonButton
              fill="clear"
              onClick={handleToggleExpansion}
              title={isExpanded ? 'Contraer menú' : 'Expandir menú'}
            >
              <IonIcon icon={isExpanded ? closeOutline : menuOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      {/* Contenido expandible del MenuTop */}
      {isExpanded && (
        <IonContent className="menu-top-content !p-0 !m-0 side-menu" scrollY={false}>
          <div className="flex flex-row">
            {/* Top menu vertical (migrado de MenuMainContent) */}
            <SimpleBar
              ref={topMenuContentRef}
              className="h-full max-h-full min-w-[64px] max-w-[64px] w-[64px]"
            >
              <TopMenu
                pages={topSectionsMenuSpecs}
                selectedSection={selectedSection}
                onSelectSection={handleSelectSection}
                className="py-4 gap-8"
                useBrandingColors={useBrandingColors}
                checkIsCurrentPage={checkIsCurrentPage}
              />
            </SimpleBar>

            {/* Section side menu (migrado de MenuOfSections) */}
            <div className="w-full max-w-full">
              <SimpleBar className="h-full max-h-full w-full max-w-full pr-2">
                <MenuOfSections 
                  selectedSection={selectedSection}
                  getMenuPages={getMenuPages}
                  userAuth={userAuth}
                  selectedInstance={selectedInstance}
                  instance={instance}
                  isAllowed={isAllowed}
                  attachPrefix={attachPrefix}
                  location={location}
                  openAccordion={openAccordion}
                  setOpenAccordion={setOpenAccordion}
                />
              </SimpleBar>
            </div>
          </div>
        </IonContent>
      )}
    </div>
  );
}

export default MenuTopNavigationController;
